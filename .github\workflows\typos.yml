# Copied from https://github.com/rerun-io/rerun_template

# https://github.com/crate-ci/typos
# Add exceptions to `.typos.toml`
# install and run locally: cargo install typos-cli && typos

name: Spell Check
on: [pull_request]

jobs:
  run:
    name: Spell Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Actions Repository
        uses: actions/checkout@v4

      - name: Check spelling of entire workspace
        uses: crate-ci/typos@master
