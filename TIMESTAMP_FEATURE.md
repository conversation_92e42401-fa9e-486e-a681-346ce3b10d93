# 时间戳功能说明

## 功能概述

SerialGUI-rs 现在支持在接收到的消息中显示时间戳。用户可以通过设置菜单控制是否显示时间戳。

## 功能特性

1. **可配置的时间戳显示**：用户可以在设置菜单中开启或关闭时间戳显示
2. **统一的时间戳格式**：时间戳格式为 `[HH:MM:SS.mmm]`，显示小时、分钟、秒和毫秒
3. **同步显示**：时间戳会同时显示在GUI日志面板和文件日志中
4. **默认启用**：新安装的应用程序默认启用时间戳显示

## 使用方法

### 启用/禁用时间戳

1. 打开应用程序
2. 点击菜单栏中的 "Settings" 菜单
3. 在设置面板中找到 "Show timestamp in received messages" 复选框
4. 勾选该复选框以启用时间戳，取消勾选以禁用时间戳
5. 设置会自动保存并立即生效

### 时间戳格式示例

启用时间戳时，接收到的消息会显示为：
```
[14:30:25.123] Hello World
[14:30:25.456] Data received from serial port
```

禁用时间戳时，消息会直接显示：
```
Hello World
Data received from serial port
```

## 技术实现

### 代码修改

1. **AppSettings 结构体**：添加了 `show_timestamp: bool` 字段
2. **设置面板**：添加了时间戳控制的复选框
3. **日志写入逻辑**：修改了 `write_log` 方法以支持条件性时间戳格式化
4. **文件日志**：自动继承时间戳设置，无需额外修改

### 时间戳格式

- 使用 `chrono` 库的 `Local::now().format("%H:%M:%S%.3f")` 格式
- 显示24小时制时间，精确到毫秒
- 格式：`[HH:MM:SS.mmm]`

## 测试

项目包含了单元测试来验证：
1. 默认设置中时间戳功能是启用的
2. 时间戳格式化逻辑正确工作
3. 启用和禁用时间戳的行为符合预期

运行测试：
```bash
cargo test
```

## 兼容性

- 该功能向后兼容，不会影响现有的配置文件
- 如果配置文件中没有 `show_timestamp` 字段，会使用默认值 `true`
- 所有现有功能保持不变
