on: 
  push:
  pull_request:
  workflow_dispatch:
  release:
    types: [created]

permissions: 
  contents: write

name: CI

env:
  RUSTFLAGS: -D warnings
  RUSTDOCFLAGS: -D warnings

jobs:
  check:
    name: Check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
      - run: sudo apt-get update && sudo apt-get install libudev-dev pkg-config
      - uses: actions-rs/cargo@v1
        with:
          command: check
          args: --all-features

  test:
    name: Test Suite
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
      - run: sudo apt-get update && sudo apt-get install libxcb-render0-dev libxcb-shape0-dev libxcb-xfixes0-dev libxkbcommon-dev libssl-dev libudev-dev pkg-config
      - uses: actions-rs/cargo@v1
        with:
          command: test
          args: --lib

  fmt:
    name: Rustfmt
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
          components: rustfmt
      - run: sudo apt-get update && sudo apt-get install libudev-dev pkg-config
      - uses: actions-rs/cargo@v1
        with:
          command: fmt
          args: --all -- --check

  clippy:
    name: Clippy
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
          components: clippy
      - run: sudo apt-get update && sudo apt-get install libudev-dev pkg-config
      - uses: actions-rs/cargo@v1
        with:
          command: clippy
          args: -- -D warnings

  build:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-latest
            TARGET: x86_64-unknown-linux-gnu

          - os: windows-latest
            TARGET: x86_64-pc-windows-msvc
            EXTENSION: .exe

    steps:
      - name: Building ${{ matrix.TARGET }}
        run: echo "${{ matrix.TARGET }}"

      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          filter: tree:0
          ref: ${{ github.ref }}

      - name: Install system dependencies
        if: contains(matrix.TARGET, 'linux')
        run: |
          sudo sed -i 's/azure.archive.ubuntu.com/archive.ubuntu.com/' /etc/apt/sources.list
          sudo apt-get -qq update
          sudo apt-get -qq -y install build-essential curl git pkg-config libudev-dev ${{ matrix.EXTRAPACKAGES }}

      - name: Install toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          target: ${{ matrix.TARGET }}
          toolchain: stable

      - name: Rust-cache
        uses: Swatinem/rust-cache@v2

      - name: Build
        run: |
          cargo build --release --target=${{ matrix.TARGET }}

      - name: Rename
        run: cp target/${{ matrix.TARGET }}/release/serialgui_rs${{ matrix.EXTENSION }} SerialGUI_rs-${{ matrix.TARGET }}${{ matrix.EXTENSION }}

      - uses: actions/upload-artifact@v4
        with:
          name: SerialGUI_rs-${{ matrix.TARGET }}${{ matrix.EXTENSION }}
          path: SerialGUI_rs-${{ matrix.TARGET }}${{ matrix.EXTENSION }}

      - uses: svenstaro/upload-release-action@v2
        name: Upload binaries to release
        if: ${{ github.event_name == 'release' }}
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: SerialGUI_rs-${{ matrix.TARGET }}${{ matrix.EXTENSION }}
          asset_name: SerialGUI_rs-${{ matrix.TARGET }}${{ matrix.EXTENSION }}
          tag: ${{ github.ref }}
          prerelease: ${{ !startsWith(github.ref, 'refs/tags/') }}
          overwrite: true

  package_macos:
    name: Package macOS App
    uses: ./.github/workflows/macos_package.yml

  package_linux:
    name: Package Linux AppImage
    uses: ./.github/workflows/linux_package.yml
