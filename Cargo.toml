[package]
name = "serialgui_rs"
version = "1.0.1"
authors = ["Lutgaru <<EMAIL>>"]
edition = "2021"
include = ["LICENSE-APACHE", "LICENSE-GPLv3", "**/*.rs", "Cargo.toml"]
rust-version = "1.81"
build = "build.rs"

[build-dependencies]
vergen-gitcl = { version = "1.0.0", features = ["build", "cargo", "rustc", "si"] }

[package.metadata.docs.rs]
all-features = true
targets = ["x86_64-unknown-linux-gnu"]

[dependencies]
egui = "0.31.1"
eframe = { version = "0.31.1", default-features = false, features = [
    "accesskit",     # Make egui compatible with screen readers. NOTE: adds a lot of dependencies.
    "default_fonts", # Embed the default egui fonts.
    "glow",          # Use the glow rendering backend. Alternative: "wgpu".
    "persistence",   # Enable restoring app state when restarting the app.
    "wayland",       # To support Linux (and CI)
] }
log = "0.4.26"
chrono = "0.4.41"
reqwest = { version = "0.12.20", features = ["json"] }
tokio = { version = "1", features = ["full"] }
serde_json = "1"
webbrowser = "1.0.5"

# You only need serde if you want app persistence:
serde = { version = "1.0.219", features = ["derive"] }
serialport = "4.7.2"

# native:
[target.'cfg(not(target_arch = "wasm32"))'.dependencies]
env_logger = "0.11.8"

[profile.release]
opt-level = 2 # optimized for size and speed

# Optimize all dependencies even in debug builds:
[profile.dev.package."*"]
opt-level = 2


[patch.crates-io]

# If you want to use the bleeding edge version of egui and eframe:
# egui = { git = "https://github.com/emilk/egui", branch = "master" }
# eframe = { git = "https://github.com/emilk/egui", branch = "master" }

# If you fork https://github.com/emilk/egui you can test with:
# egui = { path = "../egui/crates/egui" }
# eframe = { path = "../egui/crates/eframe" }
