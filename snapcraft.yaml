name: serialgui-rs
version: git
summary: A lightweight serial terminal monitor written in Rust
description: |
  SerialGUI-Rs is a cross-platform graphical serial monitor application that uses the 
  serialport-rs library as its backend and eframe for the graphical interface.

base: core22
confinement: devmode


parts:
  SerialGUI-rs:
    plugin: rust
    source: .

apps:
  SerialGUI-rs:

    command: bin/serialgui_rs
    plugs:
      - desktop
      - desktop-legacy
      - wayland
      - x11
    environment:
      WAYLAND_DISPLAY: wayland-0
      XDG_RUNTIME_DIR: /run/user/1000
    extensions: [gnome]
