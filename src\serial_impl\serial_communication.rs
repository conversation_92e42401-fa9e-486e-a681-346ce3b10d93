use serialport::{available_ports, SerialPort, SerialPortType, SerialPortInfo};
use std::io::Result;
use std::sync::{Arc, Mutex};
use std::time::Duration;
use std::{sync::mpsc, thread};

use crate::{
    communicationtrait::{CommunicationEvent, CommunicationManager, EPortState},
    serial_impl::PortSettings,
};

#[derive(Debug, Clone)]
pub struct PortDescription {
    pub port_name: String,
    pub display_name: String,
}

pub struct SerialCommunication {
    port_settings: PortSettings,
    port_state: Arc<Mutex<EPortState>>,
    port_thread: Option<thread::Join<PERSON><PERSON>le<()>>,
    tx_to_serial: Option<mpsc::Sender<Vec<u8>>>,
}
impl SerialCommunication {
    pub(crate) fn new() -> Self {
        Self {
            port_settings: PortSettings::default(),
            port_state: Arc::new(Mutex::new(EPortState::Closed)),
            port_thread: None,
            tx_to_serial: None,
        }
    }

    /// Generate a friendly description for a serial port
    fn generate_port_description(port_info: &SerialPortInfo) -> String {
        let port_name = &port_info.port_name;

        match &port_info.port_type {
            SerialPortType::UsbPort(info) => {
                let mut description = String::new();

                // Try to build a meaningful description from USB info
                if let Some(ref product) = info.product {
                    if !product.is_empty() {
                        description = product.clone();
                    }
                }

                if description.is_empty() {
                    if let Some(ref manufacturer) = info.manufacturer {
                        if !manufacturer.is_empty() {
                            description = format!("{} USB Serial", manufacturer);
                        }
                    }
                }

                if description.is_empty() {
                    description = "USB Serial Port".to_string();
                }

                format!("{} ({})", port_name, description)
            }
            SerialPortType::BluetoothPort => {
                format!("{} (Bluetooth Serial Port)", port_name)
            }
            SerialPortType::PciPort => {
                format!("{} (Hardware Serial Port)", port_name)
            }
            SerialPortType::Unknown => {
                // For unknown ports, try to infer if it might be virtual
                let port_lower = port_name.to_lowercase();
                if port_lower.contains("virtual") ||
                   port_lower.contains("com0com") ||
                   port_lower.contains("vcom") ||
                   port_lower.contains("vspe") ||  // Virtual Serial Port Emulator
                   port_lower.contains("null-modem") ||
                   // Common virtual port patterns
                   (port_lower.starts_with("com") && port_lower.len() > 4) ||  // COM ports with high numbers
                   port_lower.contains("bridge") {
                    format!("{} (Virtual Serial Port)", port_name)
                } else {
                    format!("{} (Serial Port)", port_name)
                }
            }
        }
    }
}

impl CommunicationManager for SerialCommunication {
    fn start(&mut self, tx: mpsc::Sender<CommunicationEvent>) -> Result<()> {
        {
            *self.port_state.lock().unwrap() = EPortState::Opening;
        }
        let port_settings_clone = self.port_settings.clone();
        let port_state_clone = Arc::clone(&self.port_state);
        let (tx_to_serial, rx_from_app) = mpsc::channel();
        self.tx_to_serial = Some(tx_to_serial);
        let handle = thread::spawn(move || {
            // some work here
            let mut port: Option<Box<dyn SerialPort>>;
            {
                let mut port_state = port_state_clone.lock().unwrap();
                let portopen = serialport::new(
                    port_settings_clone.port_name.clone(),
                    port_settings_clone.baudrate,
                )
                .flow_control(port_settings_clone.flowcontrol)
                .parity(port_settings_clone.parity)
                .stop_bits(port_settings_clone.stop_bits)
                .timeout(Duration::from_millis(10))
                .open();
                match portopen {
                    Ok(portopen) => {
                        // port = Some(portopen);
                        *port_state = EPortState::Open;
                        port = Some(portopen);
                    }
                    Err(e) => {
                        eprintln!(
                            "Failed to open \"{}\". Error: {}",
                            port_settings_clone.port_name, e
                        );
                        *port_state = EPortState::Closed;
                        return;
                    }
                }
            }

            while *port_state_clone.lock().unwrap() == EPortState::Open {
                if let Some(ref mut port_instance) = port {
                    let size = port_instance.bytes_to_read().unwrap_or(0);
                    if size > 0 {
                        let mut serial_buf: Vec<u8> = vec![0; size as usize];
                        port_instance.read_exact(&mut serial_buf).unwrap();
                        // self.write_log(message.unwrap_or(String::from("")).as_str());
                        tx.send(CommunicationEvent::DataReceived(serial_buf))
                            .expect("Failed to send data to GUI");
                    }
                }

                if let Ok(message) = rx_from_app.try_recv() {
                    // self.write_log(message.as_str());
                    if let Some(ref mut port_instance) = port {
                        match port_instance.write_all(&message) {
                            Ok(_) => eprintln!("Write success"),
                            Err(e) => eprintln!("{e:?}"),
                        }
                    }
                }
            }
        });
        self.port_thread = Some(handle);
        Ok(())
    }

    fn stop(&mut self) -> Result<()> {
        {
            let mut port_state = self.port_state.lock().unwrap();
            if *port_state == EPortState::Open {
                *port_state = EPortState::Closed;
            }
        }
        if let Some(handle) = self.port_thread.take() {
            handle.join().unwrap();
        }
        self.port_thread = None;
        Ok(())
    }

    fn is_running(&self) -> bool {
        let port_state = self.port_state.lock().unwrap();
        *port_state == EPortState::Open || *port_state == EPortState::Opening
    }

    fn send_data(&mut self, data: Vec<u8>) -> Result<()> {
        if let Some(tx) = &self.tx_to_serial {
            tx.send(data)
                .map_err(|e| std::io::Error::other(format!("Failed to send data: {e}")))?;
        } else {
            return Err(std::io::Error::new(
                std::io::ErrorKind::NotConnected,
                "Serial port is not open",
            ));
        }
        Ok(())
    }

    fn get_available_connections(&self) -> Vec<String> {
        let port_descriptions = self.get_available_port_descriptions();
        port_descriptions.into_iter().map(|desc| desc.display_name).collect()
    }

    fn update_settings(&mut self, settings: &dyn std::any::Any) -> Result<()> {
        if let Some(new_settings) = settings.downcast_ref::<PortSettings>() {
            self.port_settings = new_settings.clone();
            eprintln!("Updated port settings: {:?}", self.port_settings.baudrate);
            Ok(())
        } else {
            Err(std::io::Error::new(
                std::io::ErrorKind::InvalidInput,
                "Invalid settings type",
            ))
        }
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

impl SerialCommunication {
    /// Get detailed port descriptions
    pub fn get_available_port_descriptions(&self) -> Vec<PortDescription> {
        let mut port_list: Vec<PortDescription> = vec![];
        match available_ports() {
            Ok(mut ports) => {
                ports.sort_by_key(|i| i.port_name.clone());
                match ports.len() {
                    0 => println!("No ports found."),
                    1 => println!("Found 1 port:"),
                    n => println!("Found {n} ports:"),
                };

                for p in ports {
                    let display_name = Self::generate_port_description(&p);
                    println!("    {}", display_name);

                    port_list.push(PortDescription {
                        port_name: p.port_name.clone(),
                        display_name,
                    });

                    // Still print detailed info for debugging
                    match p.port_type {
                        SerialPortType::UsbPort(info) => {
                            println!("        Type: USB");
                            println!("        VID: {:04x}", info.vid);
                            println!("        PID: {:04x}", info.pid);
                            println!(
                                "        Serial Number: {}",
                                info.serial_number.as_ref().map_or("", String::as_str)
                            );
                            println!(
                                "        Manufacturer: {}",
                                info.manufacturer.as_ref().map_or("", String::as_str)
                            );
                            println!(
                                "        Product: {}",
                                info.product.as_ref().map_or("", String::as_str)
                            );
                        }
                        SerialPortType::BluetoothPort => {
                            println!("        Type: Bluetooth");
                        }
                        SerialPortType::PciPort => {
                            println!("        Type: PCI");
                        }
                        SerialPortType::Unknown => {
                            println!("        Type: Unknown");
                        }
                    }
                }
            }
            Err(e) => {
                eprintln!("{e:?}");
                eprintln!("Error listing serial ports");
            }
        }
        port_list
    }
}
