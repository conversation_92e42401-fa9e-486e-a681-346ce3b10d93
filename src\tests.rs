#[cfg(test)]
mod tests {
    use crate::generalsettings::AppSettings;

    #[test]
    fn test_default_timestamp_setting() {
        let settings = AppSettings::default();
        assert_eq!(settings.show_timestamp, true);
    }

    #[test]
    fn test_timestamp_formatting_logic() {
        use chrono::prelude::Local;

        let settings_with_timestamp = AppSettings {
            show_timestamp: true,
            ..AppSettings::default()
        };

        let settings_without_timestamp = AppSettings {
            show_timestamp: false,
            ..AppSettings::default()
        };

        let test_message = "Hello World\n";

        // Test with timestamp enabled
        let formatted_with_timestamp = if settings_with_timestamp.show_timestamp {
            format!("[{}] {}", Local::now().format("%H:%M:%S%.3f"), test_message)
        } else {
            test_message.to_string()
        };

        // Check that the formatted message contains timestamp
        assert!(formatted_with_timestamp.contains("["));
        assert!(formatted_with_timestamp.contains("]"));
        assert!(formatted_with_timestamp.contains("Hello World"));

        // Test with timestamp disabled
        let formatted_without_timestamp = if settings_without_timestamp.show_timestamp {
            format!("[{}] {}", Local::now().format("%H:%M:%S%.3f"), test_message)
        } else {
            test_message.to_string()
        };

        // Check that the formatted message doesn't contain timestamp format
        assert_eq!(formatted_without_timestamp, test_message);
    }
}
