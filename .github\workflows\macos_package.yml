name: macOS App Packaging

on:
  workflow_call:

jobs:
  package-macos:
    # The runner is selected from the matrix
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          # Configuration for ARM architecture (Apple Silicon)
          - os: macos-latest
            TARGET: aarch64-apple-darwin
            ARCH_SUFFIX: MacOS-arm64 # Suffix for file names, if needed

          # Configuration for Intel architecture
          - os: macos-13 # macos-13 is a good option for x86_64, macos-latest might be ARM
            TARGET: x86_64-apple-darwin
            ARCH_SUFFIX: MacOS-x64 # Suffix for file names, if needed
    env:
      RUSTFLAGS: -D warnings
      RUSTDOCFLAGS: -D warnings
      # Set the minimum macOS deployment target for the compiled binary
      MACOSX_DEPLOYMENT_TARGET: '12.0'

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          filter: tree:0
          ref: ${{ github.ref }}

      - name: Install toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: stable
          # Use the TARGET variable from the matrix to install the correct toolchain
          target: ${{ matrix.TARGET }}

      - name: Build release
        run: |
          # Use the TARGET variable from the matrix to build the correct binary
          cargo build --release --target=${{ matrix.TARGET }}

      - name: Package .app bundle
        run: |
          APP_NAME="SerialGUI_rs"
          # Get the TARGET from the GitHub Actions environment variable
          CURRENT_TARGET="${{ matrix.TARGET }}"
          BIN_PATH="target/$CURRENT_TARGET/release/$APP_NAME"
          APP_BUNDLE="$APP_NAME.app" # The bundle name is the same for both architectures

          echo "Creating the .app bundle for architecture: $CURRENT_TARGET"

          mkdir -p "$APP_BUNDLE/Contents/MacOS"
          mkdir -p "$APP_BUNDLE/Contents/Resources"

          # Copy the binary
          cp "$BIN_PATH" "$APP_BUNDLE/Contents/MacOS/$APP_NAME"

          # Copy the icon (ensure 'assets/SerialGUI-rs-logo.icns' exists)
          cp assets/SerialGUI-rs-logo.icns "$APP_BUNDLE/Contents/Resources/$APP_NAME.icns"

          # Copy the Info.plist from a template and replace placeholders
          # Make sure to create the file 'assets/Info.plist.template' in your repository
          cp assets/Info.plist.template "$APP_BUNDLE/Contents/Info.plist"
          # Replace __APP_NAME__ with the actual application name
          sed -i '' "s/__APP_NAME__/$APP_NAME/g" "$APP_BUNDLE/Contents/Info.plist"
          # Replace __ARCH_TARGET__ with the architecture (e.g., arm64 or x86)
          sed -i '' "s/__ARCH_TARGET__/$(echo $CURRENT_TARGET | cut -d'-' -f1)/g" "$APP_BUNDLE/Contents/Info.plist"

          # It's good practice to set executable permissions for the binary
          chmod +x "$APP_BUNDLE/Contents/MacOS/$APP_NAME"

      - name: Create DMG
        run: |
          APP_NAME="SerialGUI_rs"
          APP_BUNDLE="$APP_NAME.app"
          DMG_NAME="${APP_NAME}-${{ matrix.ARCH_SUFFIX }}.dmg" # Dynamic DMG name
          VOL_NAME="${APP_NAME} (${{ matrix.ARCH_SUFFIX }})" # Dynamic volume name

          mkdir dmg_folder
          # Move the .app bundle created in the previous step to the DMG folder
          mv "$APP_BUNDLE" dmg_folder/

          echo "Creating the DMG file: $DMG_NAME"
          hdiutil create -volname "$VOL_NAME" \
                         -srcfolder dmg_folder \
                         -ov -format UDZO "$DMG_NAME"

      - name: Upload DMG artifact
        uses: actions/upload-artifact@v4
        with:
          # Dynamic artifact name to differentiate between architectures
          name: ${{ github.job }}-${{ matrix.ARCH_SUFFIX }}
          # Dynamic DMG file path
          path: SerialGUI_rs-${{ matrix.ARCH_SUFFIX }}.dmg

      - name: Upload binaries to release
        uses: svenstaro/upload-release-action@v2
        # This step will only run if the workflow was triggered by a 'release' event
        if: ${{ github.event_name == 'release' }}
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          # Use the correct dynamic DMG file path for the upload
          file: SerialGUI_rs-${{ matrix.ARCH_SUFFIX }}.dmg
          # Use the correct dynamic asset name for the release
          asset_name: SerialGUI_rs-${{ matrix.ARCH_SUFFIX }}.dmg
          tag: ${{ github.ref }}
          prerelease: ${{ !startsWith(github.ref, 'refs/tags/') }}
          overwrite: true
